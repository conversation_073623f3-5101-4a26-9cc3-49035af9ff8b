"use client";

import React, { useState } from "react";
import { Play, ArrowLeft } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RoomSettings } from "@/lib/types/multiplayer";
import { Difficulty } from "@/lib/constants";

interface CreateRoomViewProps {
  onCreateRoom: (roomName: string, settings: RoomSettings) => void;
  onBack: () => void;
  isCreating?: boolean;
}

const CreateRoomView: React.FC<CreateRoomViewProps> = ({
  onCreateRoom,
  onBack,
  isCreating = false,
}) => {
  const [roomName, setRoomName] = useState("");
  const [settings, setSettings] = useState<RoomSettings>({
    maxRoomSize: 4,
    difficulty: "easy",
    gameMode: "classic",
    timeLimit: 30,
  });

  const handleCreateRoom = () => {
    if (roomName.trim()) {
      onCreateRoom(roomName.trim(), settings);
    }
  };

  const isFormValid = roomName.trim().length >= 3;

  return (
    <div className="min-h-screen h-screen sm:min-h-screen sm:h-auto bg-background overflow-y-auto">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 py-4 sm:py-6">
        <div className="mb-8">
          <div className="flex items-center justify-center mb-3 sm:mb-6">
            <div className="bg-card rounded-2xl px-4 py-2 shadow border">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="icon-sm"
                  onClick={onBack}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <ArrowLeft className="w-4 h-4" />
                </Button>

                <div className="w-px h-6 bg-border"></div>

                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-foreground">
                    CREATE ROOM
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <Card className="mb-3 sm:mb-6 shadow-card hover:shadow-card-hover transition-all duration-300">
          <CardContent className="p-3 sm:p-4">
            <div className="text-center mb-4 sm:mb-8">
              <h1 className="text-lg sm:text-xl font-semibold text-foreground mb-1 sm:mb-2">
                Create Multiplayer Room
              </h1>
              <p className="text-muted-foreground text-sm">
                Set up a new game room for you and your friends
              </p>
            </div>

            <div className="space-y-4 sm:space-y-6">
              <Input
                placeholder="Room name"
                value={roomName}
                onChange={(e) => setRoomName(e.target.value)}
                className="text-center"
                maxLength={30}
              />

              <div className="grid grid-cols-2 gap-3 sm:gap-4">
                <Select
                  value={settings.maxRoomSize.toString()}
                  onValueChange={(value) =>
                    setSettings((prev) => ({
                      ...prev,
                      maxRoomSize: parseInt(value),
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Players" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="2">2 Players</SelectItem>
                    <SelectItem value="4">4 Players</SelectItem>
                    <SelectItem value="6">6 Players</SelectItem>
                    <SelectItem value="8">8 Players</SelectItem>
                  </SelectContent>
                </Select>

                <Select
                  value={settings.difficulty}
                  onValueChange={(value) =>
                    setSettings((prev) => ({
                      ...prev,
                      difficulty: value as Difficulty,
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Difficulty" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="easy">Easy</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="hard">Hard</SelectItem>
                    <SelectItem value="expert">Expert</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button
                onClick={handleCreateRoom}
                disabled={!isFormValid || isCreating}
                className="w-full"
                size="lg"
              >
                {isCreating ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4 mr-2" />
                    Create Room
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CreateRoomView;
