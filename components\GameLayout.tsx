"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { Home } from "lucide-react";
import { Button } from "@/components/ui/button";
import SettingsMenu from "@/components/SettingsMenu";
import LevelBadge from "@/components/LevelBadge";
import { useGameSettings } from "@/lib/hooks/useGameSettings";
import { useSoundEffect } from "@/lib/hooks/useSoundEffect";
import { AUDIO_URLS, AUDIO_URLS_KEYS, Difficulty } from "@/lib/constants";

interface GameLayoutProps {
  children: React.ReactNode;
  showLevel?: boolean;
  level?: Difficulty;
  title?: string;
  showHome?: boolean;
}

const GameLayout: React.FC<GameLayoutProps> = ({
  children,
  showLevel = false,
  level = "easy",
  title,
  showHome = false,
}) => {
  const router = useRouter();
  const { settings, updateSetting } = useGameSettings();
  const [settingsOpen, setSettingsOpen] = useState(false);

  const { playSound: playButtonClickSound } = useSoundEffect({
    audioUrl: AUDIO_URLS.BUTTON_CLICK,
    volume: 0.5,
    cacheKey: AUDIO_URLS_KEYS.BUTTON_CLICK,
  });

  const toggleDarkMode = () => {
    const nextDark = !settings.darkMode;
    updateSetting("darkMode", nextDark);
    document.cookie = `theme=${
      nextDark ? "dark" : "light"
    }; path=/; max-age=31536000`;
    playButtonClickSound();
  };

  const toggleSound = () => {
    const newValue = !settings.soundEffectsEnabled;
    updateSetting("soundEffectsEnabled", newValue);
    playButtonClickSound();
  };

  const goHome = () => {
    playButtonClickSound();
    router.push("/");
  };

  return (
    <div className="min-h-screen h-screen sm:min-h-screen sm:h-auto bg-background overflow-y-auto">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 py-4 sm:py-6">
        <div className="mb-8">
          <div className="flex items-center justify-center mb-3 sm:mb-6">
            <div className="bg-card rounded-2xl px-4 py-2 shadow border">
              <div className="flex items-center gap-4">
                {showHome && (
                  <>
                    <Button
                      variant="ghost"
                      size="icon-sm"
                      onClick={goHome}
                      className="text-muted-foreground hover:text-foreground"
                    >
                      <Home className="w-4 h-4" />
                    </Button>
                    
                    <div className="w-px h-6 bg-border"></div>
                  </>
                )}
                
                {showLevel && (
                  <>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-foreground">
                        LEVEL
                      </span>
                      <LevelBadge difficulty={level} />
                    </div>
                    
                    <div className="w-px h-6 bg-border"></div>
                  </>
                )}

                {title && (
                  <>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-foreground">
                        {title}
                      </span>
                    </div>
                    
                    <div className="w-px h-6 bg-border"></div>
                  </>
                )}

                <SettingsMenu
                  settingsOpen={settingsOpen}
                  setSettingsOpen={setSettingsOpen}
                  setShowDifficultyDialog={() => {}} // Can be passed as prop if needed
                  toggleSound={toggleSound}
                  toggleDarkMode={toggleDarkMode}
                  settings={settings}
                />
              </div>
            </div>
          </div>
        </div>

        {children}
      </div>
    </div>
  );
};

export default GameLayout;
