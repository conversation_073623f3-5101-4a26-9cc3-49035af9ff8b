"use client";

import { SocketProvider } from "@/lib/context/SocketContext";

export const dynamic = "force-dynamic";

interface MultiplayerPageProps {
  searchParams: Promise<{ [key: string]: string | undefined }>;
}

export default function MultiplayerPage({
  searchParams,
}: MultiplayerPageProps) {
  return (
    <SocketProvider wsUrl={process.env.NEXT_PUBLIC_SOCKET_URL}>
      <div>MULTIPLAYER</div>
    </SocketProvider>
  );
}
