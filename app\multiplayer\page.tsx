"use client";

import { SocketProvider } from "@/lib/context/SocketContext";
import MultiplayerLobby from "@/components/multiplayer/MultiplayerLobby";
import { RoomSettings } from "@/lib/types/multiplayer";

export const dynamic = "force-dynamic";

interface MultiplayerPageProps {
  searchParams: Promise<{ [key: string]: string | undefined }>;
}

export default function MultiplayerPage({}: MultiplayerPageProps) {
  const handleCreateRoom = (roomName: string, settings: RoomSettings) => {
    // TODO: Implement room creation logic
    console.log("Creating room:", roomName, settings);
  };

  const handleJoinRoom = (roomCode: string, username: string) => {
    // TODO: Implement room joining logic
    console.log("Joining room:", roomCode, "as", username);
  };

  return (
    <SocketProvider wsUrl={process.env.NEXT_PUBLIC_SOCKET_URL}>
      <MultiplayerLobby
        onCreateRoom={handleCreateRoom}
        onJoinRoom={handleJoinRoom}
      />
    </SocketProvider>
  );
}
