@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 2px);
  --radius-md: var(--radius);
  --radius-lg: calc(var(--radius) + 4px);
  --radius-xl: calc(var(--radius) + 8px);
  --radius-2xl: calc(var(--radius) + 12px);
  --radius-3xl: calc(var(--radius) + 16px);
}

:root {
  --radius: 1.25rem; /* Increased for modern, softer look */
  --background: oklch(0.99 0.001 240); /* Softer, more neutral background */
  --foreground: oklch(0.15 0.005 240); /* Slightly softer text */
  --card: oklch(1 0 0); /* Pure white cards for clean look */
  --card-foreground: oklch(0.15 0.005 240);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.005 240);
  --primary: oklch(0.5 0.2 240); /* Modern blue, less saturated */
  --primary-foreground: oklch(0.99 0.001 240);
  --secondary: oklch(0.96 0.008 240); /* Softer secondary */
  --secondary-foreground: oklch(0.15 0.005 240);
  --muted: oklch(0.97 0.005 240); /* Very light muted */
  --muted-foreground: oklch(0.55 0.015 240); /* Better contrast */
  --accent: oklch(0.95 0.008 240);
  --accent-foreground: oklch(0.15 0.005 240);
  --destructive: oklch(0.58 0.2 25); /* Modern darker red */
  --border: oklch(0.92 0.005 240); /* Softer borders */
  --input: oklch(0.95 0.005 240);
  --ring: oklch(0.5 0.2 240);

  /* Modern game-specific colors */
  --success: oklch(0.6 0.15 140); /* Modern green */
  --success-foreground: oklch(0.99 0.001 240);
  --warning: oklch(0.7 0.15 60); /* Modern yellow */
  --warning-foreground: oklch(0.15 0.005 240);
  --info: oklch(0.6 0.15 220); /* Modern blue */
  --info-foreground: oklch(0.99 0.001 240);
  --chart-1: oklch(0.55 0.18 15); /* Orange */
  --chart-2: oklch(0.5 0.2 140); /* Green */
  --chart-3: oklch(0.45 0.25 260); /* Blue */
  --chart-4: oklch(0.7 0.15 320); /* Pink */
  --chart-5: oklch(0.6 0.18 60); /* Yellow */
  --sidebar: oklch(0.98 0.002 240);
  --sidebar-foreground: oklch(0.12 0.008 240);
  --sidebar-primary: oklch(0.45 0.25 260);
  --sidebar-primary-foreground: oklch(0.98 0.002 240);
  --sidebar-accent: oklch(0.94 0.012 240);
  --sidebar-accent-foreground: oklch(0.12 0.008 240);
  --sidebar-border: oklch(0.88 0.01 240);
  --sidebar-ring: oklch(0.45 0.25 260);
}

.dark {
  --background: oklch(0.18 0.002 240); /* #2e2e2e */
  --foreground: oklch(0.98 0.002 240);
  --card: oklch(0.24 0 0); /* #1f1f20 - adjusted to be more visible */
  --card-foreground: oklch(0.98 0.002 240);
  --popover: oklch(0.24 0 0); /* #1f1f20 - matching card background */
  --popover-foreground: oklch(0.98 0.002 240);
  --primary: oklch(0.6 0.22 260);
  --primary-foreground: oklch(0.98 0.002 240);
  --secondary: oklch(0.17 0.002 240); /* Slightly lighter than card */
  --secondary-foreground: oklch(0.98 0.002 240);
  --muted: oklch(0.15 0.002 240); /* Slightly darker than card */
  --muted-foreground: oklch(0.6 0.015 240);
  --accent: oklch(0.17 0.002 240); /* Between background and card */
  --accent-foreground: oklch(0.98 0.002 240);
  --destructive: oklch(0.58 0.2 25); /* Modern darker red */
  --border: oklch(0.19 0.002 240); /* Slightly lighter than background */
  --input: oklch(0.17 0.002 240); /* Same as secondary */
  --ring: oklch(0.6 0.22 260);
  --chart-1: oklch(0.65 0.15 15);
  --chart-2: oklch(0.6 0.18 140);
  --chart-3: oklch(0.6 0.22 260);
  --chart-4: oklch(0.75 0.12 320);
  --chart-5: oklch(0.7 0.15 60);
  --sidebar: oklch(0.16 0.002 240); /* Same as card */
  --sidebar-foreground: oklch(0.98 0.002 240);
  --sidebar-primary: oklch(0.6 0.22 260);
  --sidebar-primary-foreground: oklch(0.12 0.008 240);
  --sidebar-accent: oklch(0.17 0.002 240); /* Same as secondary */
  --sidebar-accent-foreground: oklch(0.98 0.002 240);
  --sidebar-border: oklch(0.19 0.002 240); /* Same as border */
  --sidebar-ring: oklch(0.6 0.22 260);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-sans), system-ui, sans-serif;
  }

  .shadow-card {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05),
      0 2px 4px -1px rgba(0, 0, 0, 0.03);
  }

  .shadow-card-hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.08),
      0 4px 6px -2px rgba(0, 0, 0, 0.04);
  }

  .shadow-flag {
    box-shadow: 0 2px 8px -2px rgba(0, 0, 0, 0.12),
      0 4px 12px -4px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(0, 0, 0, 0.05);
  }

  .animate-score-popup {
    animation: score-popup 1.5s ease-out;
  }

  @keyframes score-popup {
    0% {
      opacity: 0;
      transform: translateY(0) scale(0.8);
    }
    30% {
      opacity: 1;
      transform: translateY(-20px) scale(1);
    }
    100% {
      opacity: 0;
      transform: translateY(-40px) scale(1);
    }
  }

  @keyframes bounce-gentle {
    0%,
    100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-2px);
    }
  }

  .animate-scale-in {
    animation: scale-in 0.2s ease-out;
  }

  @keyframes scale-in {
    0% {
      transform: scale(0.95);
      opacity: 0.8;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }
}
