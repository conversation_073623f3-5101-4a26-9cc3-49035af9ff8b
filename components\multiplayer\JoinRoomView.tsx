"use client";

import React, { useState } from "react";
import { Users, LogIn, ArrowLeft, Hash, Search } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface JoinRoomViewProps {
  onJoinRoom: (roomCode: string, username: string) => void;
  onBack: () => void;
  isJoining?: boolean;
  error?: string;
}

const JoinRoomView: React.FC<JoinRoomViewProps> = ({
  onJoinRoom,
  onBack,
  isJoining = false,
  error,
}) => {
  const [roomCode, setRoomCode] = useState("");
  const [username, setUsername] = useState("");

  const handleJoinRoom = () => {
    if (roomCode.trim() && username.trim()) {
      onJoinRoom(roomCode.trim().toUpperCase(), username.trim());
    }
  };

  const handleRoomCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Convert to uppercase and limit to 6 characters
    const value = e.target.value.toUpperCase().slice(0, 6);
    setRoomCode(value);
  };

  const isFormValid = roomCode.trim().length >= 4 && username.trim().length >= 2;

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 py-6 sm:py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-center mb-6">
            <div className="bg-card rounded-2xl px-4 py-2 shadow border">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="icon-sm"
                  onClick={onBack}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <ArrowLeft className="w-4 h-4" />
                </Button>
                
                <div className="w-px h-6 bg-border"></div>
                
                <div className="flex items-center gap-2">
                  <LogIn className="w-4 h-4 text-primary" />
                  <span className="text-sm font-medium text-foreground">
                    JOIN ROOM
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Card */}
        <Card className="shadow-card hover:shadow-card-hover transition-all duration-300">
          <CardHeader>
            <CardTitle className="text-center flex items-center justify-center gap-2">
              <Users className="w-5 h-5 text-primary" />
              Join Game Room
            </CardTitle>
            <CardDescription className="text-center">
              Enter the room code shared by your friend to join their game
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Error Message */}
            {error && (
              <div className="bg-destructive/10 border border-destructive/20 rounded-xl p-4">
                <p className="text-sm text-destructive text-center font-medium">
                  {error}
                </p>
              </div>
            )}

            {/* Room Code */}
            <div className="space-y-2">
              <Label htmlFor="roomCode" className="text-sm font-medium flex items-center gap-2">
                <Hash className="w-4 h-4" />
                Room Code
              </Label>
              <Input
                id="roomCode"
                placeholder="Enter 4-6 character room code"
                value={roomCode}
                onChange={handleRoomCodeChange}
                className="h-12 rounded-xl text-center text-lg font-mono tracking-wider uppercase"
                maxLength={6}
              />
              <p className="text-xs text-muted-foreground text-center">
                Room codes are usually 4-6 characters long
              </p>
            </div>

            {/* Username */}
            <div className="space-y-2">
              <Label htmlFor="username" className="text-sm font-medium">
                Your Username
              </Label>
              <Input
                id="username"
                placeholder="Enter your display name"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="h-11 rounded-xl"
                maxLength={20}
              />
              <p className="text-xs text-muted-foreground">
                {username.length}/20 characters
              </p>
            </div>

            {/* Join Button */}
            <div className="pt-4">
              <Button
                onClick={handleJoinRoom}
                disabled={!isFormValid || isJoining}
                className="w-full"
                size="lg"
              >
                {isJoining ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                    Joining Room...
                  </>
                ) : (
                  <>
                    <LogIn className="w-4 h-4 mr-2" />
                    Join Room
                  </>
                )}
              </Button>
            </div>

            {/* Info */}
            <div className="bg-muted/50 rounded-xl p-4 text-center">
              <p className="text-sm text-muted-foreground">
                Ask your friend for the room code to join their game
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Alternative: Browse Public Rooms */}
        <div className="mt-6">
          <Card className="shadow-card hover:shadow-card-hover transition-all duration-300">
            <CardContent className="p-4">
              <div className="text-center">
                <h3 className="text-sm font-medium text-foreground mb-2 flex items-center justify-center gap-2">
                  <Search className="w-4 h-4" />
                  Browse Public Rooms
                </h3>
                <p className="text-xs text-muted-foreground mb-4">
                  Join an existing public room or wait for others to join
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  disabled={true} // TODO: Implement public rooms feature
                >
                  Coming Soon
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default JoinRoomView;
