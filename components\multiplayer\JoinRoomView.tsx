"use client";

import React, { useState } from "react";
import { LogIn, ArrowLeft } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";

interface JoinRoomViewProps {
  onJoinRoom: (roomCode: string, username: string) => void;
  onBack: () => void;
  isJoining?: boolean;
  error?: string;
}

const JoinRoomView: React.FC<JoinRoomViewProps> = ({
  onJoinRoom,
  onBack,
  isJoining = false,
  error,
}) => {
  const [roomCode, setRoomCode] = useState("");
  const [username, setUsername] = useState("");

  const handleJoinRoom = () => {
    if (roomCode.trim() && username.trim()) {
      onJoinRoom(roomCode.trim().toUpperCase(), username.trim());
    }
  };

  const handleRoomCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase().slice(0, 6);
    setRoomCode(value);
  };

  const isFormValid = roomCode.trim().length >= 4 && username.trim().length >= 2;

  return (
    <div className="min-h-screen h-screen sm:min-h-screen sm:h-auto bg-background overflow-y-auto">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 py-4 sm:py-6">
        <div className="mb-8">
          <div className="flex items-center justify-center mb-3 sm:mb-6">
            <div className="bg-card rounded-2xl px-4 py-2 shadow border">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="icon-sm"
                  onClick={onBack}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <ArrowLeft className="w-4 h-4" />
                </Button>

                <div className="w-px h-6 bg-border"></div>

                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-foreground">
                    JOIN ROOM
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <Card className="mb-3 sm:mb-6 shadow-card hover:shadow-card-hover transition-all duration-300">
          <CardContent className="p-3 sm:p-4">
            <div className="text-center mb-4 sm:mb-8">
              <h1 className="text-lg sm:text-xl font-semibold text-foreground mb-1 sm:mb-2">
                Join Multiplayer Room
              </h1>
              <p className="text-muted-foreground text-sm">
                Enter the room code to join an existing game
              </p>
            </div>

            <div className="space-y-4 sm:space-y-6">
              {error && (
                <div className="bg-destructive/10 border border-destructive/20 rounded-md p-3 text-center">
                  <p className="text-sm text-destructive font-medium">
                    {error}
                  </p>
                </div>
              )}

              <Input
                placeholder="Room code"
                value={roomCode}
                onChange={handleRoomCodeChange}
                className="text-center font-mono tracking-wider uppercase"
                maxLength={6}
              />

              <Input
                placeholder="Your username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="text-center"
                maxLength={20}
              />

              <Button
                onClick={handleJoinRoom}
                disabled={!isFormValid || isJoining}
                className="w-full"
                size="lg"
              >
                {isJoining ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                    Joining...
                  </>
                ) : (
                  <>
                    <LogIn className="w-4 h-4 mr-2" />
                    Join Room
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default JoinRoomView;
