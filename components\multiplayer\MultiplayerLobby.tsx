"use client";

import React, { useState } from "react";
import { Users, Plus, LogIn, ArrowLeft, Crown, Gamepad2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import CreateRoomView from "./CreateRoomView";
import JoinRoomView from "./JoinRoomView";
import { RoomSettings } from "@/lib/types/multiplayer";

interface MultiplayerLobbyProps {
  onCreateRoom: (roomName: string, settings: RoomSettings) => void;
  onJoinRoom: (roomCode: string, username: string) => void;
  onBack: () => void;
  isCreating?: boolean;
  isJoining?: boolean;
  joinError?: string;
}

type View = "lobby" | "create" | "join";

const MultiplayerLobby: React.FC<MultiplayerLobbyProps> = ({
  onCreateRoom,
  onJoinRoom,
  onBack,
  isCreating = false,
  isJoining = false,
  joinError,
}) => {
  const [currentView, setCurrentView] = useState<View>("lobby");

  const handleBackToLobby = () => {
    setCurrentView("lobby");
  };

  if (currentView === "create") {
    return (
      <CreateRoomView
        onCreateRoom={onCreateRoom}
        onBack={handleBackToLobby}
        isCreating={isCreating}
      />
    );
  }

  if (currentView === "join") {
    return (
      <JoinRoomView
        onJoinRoom={onJoinRoom}
        onBack={handleBackToLobby}
        isJoining={isJoining}
        error={joinError}
      />
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 py-6 sm:py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-center mb-6">
            <div className="bg-card rounded-2xl px-4 py-2 shadow border">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="icon-sm"
                  onClick={onBack}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <ArrowLeft className="w-4 h-4" />
                </Button>
                
                <div className="w-px h-6 bg-border"></div>
                
                <div className="flex items-center gap-2">
                  <Gamepad2 className="w-4 h-4 text-primary" />
                  <span className="text-sm font-medium text-foreground">
                    MULTIPLAYER
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Welcome Card */}
        <Card className="mb-6 shadow-card hover:shadow-card-hover transition-all duration-300">
          <CardHeader>
            <CardTitle className="text-center flex items-center justify-center gap-2">
              <Users className="w-6 h-6 text-primary" />
              Multiplayer Flag Game
            </CardTitle>
            <CardDescription className="text-center">
              Challenge your friends in real-time flag guessing competitions
            </CardDescription>
          </CardHeader>
        </Card>

        {/* Action Cards */}
        <div className="grid gap-4 sm:gap-6">
          {/* Create Room */}
          <Card className="shadow-card hover:shadow-card-hover transition-all duration-300 cursor-pointer group">
            <CardContent 
              className="p-6 text-center"
              onClick={() => setCurrentView("create")}
            >
              <div className="flex flex-col items-center space-y-4">
                <div className="w-16 h-16 bg-primary/10 rounded-2xl flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                  <Crown className="w-8 h-8 text-primary" />
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-foreground mb-2">
                    Create Room
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Start a new game room and invite your friends to join
                  </p>
                  
                  <Button 
                    className="w-full sm:w-auto"
                    size="lg"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Create New Room
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Join Room */}
          <Card className="shadow-card hover:shadow-card-hover transition-all duration-300 cursor-pointer group">
            <CardContent 
              className="p-6 text-center"
              onClick={() => setCurrentView("join")}
            >
              <div className="flex flex-col items-center space-y-4">
                <div className="w-16 h-16 bg-secondary/10 rounded-2xl flex items-center justify-center group-hover:bg-secondary/20 transition-colors">
                  <LogIn className="w-8 h-8 text-secondary-foreground" />
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-foreground mb-2">
                    Join Room
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Enter a room code to join an existing game
                  </p>
                  
                  <Button 
                    variant="secondary"
                    className="w-full sm:w-auto"
                    size="lg"
                  >
                    <LogIn className="w-4 h-4 mr-2" />
                    Join Existing Room
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Features Info */}
        <div className="mt-8">
          <Card className="shadow-card">
            <CardContent className="p-4">
              <h3 className="text-sm font-medium text-foreground mb-3 text-center">
                Multiplayer Features
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-center">
                <div className="space-y-1">
                  <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center mx-auto">
                    <Users className="w-4 h-4 text-primary" />
                  </div>
                  <p className="text-xs font-medium text-foreground">Up to 8 Players</p>
                  <p className="text-xs text-muted-foreground">Compete with friends</p>
                </div>
                
                <div className="space-y-1">
                  <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center mx-auto">
                    <Gamepad2 className="w-4 h-4 text-primary" />
                  </div>
                  <p className="text-xs font-medium text-foreground">Real-time Play</p>
                  <p className="text-xs text-muted-foreground">Live scoring</p>
                </div>
                
                <div className="space-y-1">
                  <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center mx-auto">
                    <Crown className="w-4 h-4 text-primary" />
                  </div>
                  <p className="text-xs font-medium text-foreground">Custom Settings</p>
                  <p className="text-xs text-muted-foreground">Your rules</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default MultiplayerLobby;
