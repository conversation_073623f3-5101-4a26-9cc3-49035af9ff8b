"use client";

import React, { useState } from "react";
import { Users, Plus, LogIn, ArrowLeft, Crown, Gamepad2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import CreateRoomView from "./CreateRoomView";
import JoinRoomView from "./JoinRoomView";
import SettingsMenu from "@/components/SettingsMenu";
import LevelBadge from "@/components/LevelBadge";
import { RoomSettings } from "@/lib/types/multiplayer";
import { useGameSettings } from "@/lib/hooks/useGameSettings";
import { useSoundEffect } from "@/lib/hooks/useSoundEffect";
import { AUDIO_URLS, AUDIO_URLS_KEYS, Difficulty } from "@/lib/constants";

interface MultiplayerLobbyProps {
  onCreateRoom: (roomName: string, settings: RoomSettings) => void;
  onJoinRoom: (roomCode: string, username: string) => void;
  onBack: () => void;
  isCreating?: boolean;
  isJoining?: boolean;
  joinError?: string;
}

type View = "lobby" | "create" | "join";

const MultiplayerLobby: React.FC<MultiplayerLobbyProps> = ({
  onCreateRoom,
  onJoinRoom,
  onBack,
  isCreating = false,
  isJoining = false,
  joinError,
}) => {
  const [currentView, setCurrentView] = useState<View>("lobby");
  const { settings, updateSetting } = useGameSettings();
  const [settingsOpen, setSettingsOpen] = useState(false);

  const { playSound: playButtonClickSound } = useSoundEffect({
    audioUrl: AUDIO_URLS.BUTTON_CLICK,
    volume: 0.5,
    cacheKey: AUDIO_URLS_KEYS.BUTTON_CLICK,
  });

  const toggleDarkMode = () => {
    const nextDark = !settings.darkMode;
    updateSetting("darkMode", nextDark);
    document.cookie = `theme=${
      nextDark ? "dark" : "light"
    }; path=/; max-age=31536000`;
    playButtonClickSound();
  };

  const toggleSound = () => {
    const newValue = !settings.soundEffectsEnabled;
    updateSetting("soundEffectsEnabled", newValue);
    playButtonClickSound();
  };

  const handleBackToLobby = () => {
    setCurrentView("lobby");
  };

  if (currentView === "create") {
    return (
      <CreateRoomView
        onCreateRoom={onCreateRoom}
        onBack={handleBackToLobby}
        isCreating={isCreating}
      />
    );
  }

  if (currentView === "join") {
    return (
      <JoinRoomView
        onJoinRoom={onJoinRoom}
        onBack={handleBackToLobby}
        isJoining={isJoining}
        error={joinError}
      />
    );
  }

  return (
    <div className="min-h-screen h-screen sm:min-h-screen sm:h-auto bg-background overflow-y-auto">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 py-4 sm:py-6">
        <div className="mb-8">
          <div className="flex items-center justify-center mb-3 sm:mb-6">
            <div className="bg-card rounded-2xl px-4 py-2 shadow border">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="icon-sm"
                  onClick={onBack}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <ArrowLeft className="w-4 h-4" />
                </Button>

                <div className="w-px h-6 bg-border"></div>

                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-foreground">
                    MULTIPLAYER
                  </span>
                </div>

                <div className="w-px h-6 bg-border"></div>

                <SettingsMenu
                  settingsOpen={settingsOpen}
                  setSettingsOpen={setSettingsOpen}
                  setShowDifficultyDialog={() => {}} // Not needed in multiplayer lobby
                  toggleSound={toggleSound}
                  toggleDarkMode={toggleDarkMode}
                  settings={settings}
                />
              </div>
            </div>
          </div>
        </div>

        <Card className="mb-3 sm:mb-6 shadow-card hover:shadow-card-hover transition-all duration-300">
          <CardContent className="p-3 sm:p-4">
            <div className="text-center mb-4 sm:mb-8">
              <h1 className="text-lg sm:text-xl font-semibold text-foreground mb-1 sm:mb-2">
                flags.games Multiplayer
              </h1>
              <p className="text-muted-foreground text-sm">
                Challenge your friends in real-time flag guessing competitions
              </p>
            </div>

            <div className="grid gap-3 sm:gap-4">
              <Button
                onClick={() => setCurrentView("create")}
                className="w-full"
                size="lg"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Room
              </Button>

              <Button
                onClick={() => setCurrentView("join")}
                variant="outline"
                className="w-full"
                size="lg"
              >
                <LogIn className="w-4 h-4 mr-2" />
                Join Room
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default MultiplayerLobby;
