import { Socket } from "socket.io-client";

export interface CustomSocket extends Socket {
  data?: {
    userId: string;
    roomId: string;
    isAdmin: boolean;
  };
}

export const SOCKET_ACTIONS = {
  // Connection
  CONNECT: "connect",
  DISCONNECT: "disconnect",
  
  // Room management
  CREATE_ROOM: "create_room",
  JOIN_ROOM: "join_room",
  LEAVE_ROOM: "leave_room",
  GET_ROOM_INFO: "get_room_info",
  ROOM_UPDATED: "room_updated",
  
  // User management
  GET_USER_INFO: "get_user_info",
  USER_JOINED: "user_joined",
  USER_LEFT: "user_left",
  SET_ADMIN: "set_admin",
  KICK_USER: "kick_user",
  
  // Game management
  START_GAME: "start_game",
  STOP_GAME: "stop_game",
  PAUSE_GAME: "pause_game",
  RESUME_GAME: "resume_game",
  SUBMIT_ANSWER: "submit_answer",
  NEXT_QUESTION: "next_question",
  GAME_STATE_UPDATE: "game_state_update",
  
  // Settings
  UPDATE_SETTINGS: "update_settings",
  SETTINGS_UPDATED: "settings_updated",
  
  // Errors
  ERROR: "error",
} as const;

export type SocketAction = typeof SOCKET_ACTIONS[keyof typeof SOCKET_ACTIONS];

// WebSocket Message Types for the new server
export interface WebSocketMessage {
  type: string;
  data?: any;
  timestamp?: number;
}

export interface CreateRoomData {
  username: string;
  userId: string;
  roomName: string;
  settings?: Partial<RoomSettings>;
}

export interface JoinRoomData {
  inviteCode: string;
  username: string;
  userId: string;
}

export interface SubmitAnswerData {
  answer: string;
  questionId?: string;
}

export interface UpdateSettingsData {
  settings: Partial<RoomSettings>;
}

export interface KickUserData {
  userId: string;
}

export interface RoomSettings {
  maxRoomSize: number;
  difficulty: string;
  gameMode: string;
  timeLimit?: number;
}
