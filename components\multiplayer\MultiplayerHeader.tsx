"use client";

import React, { useState } from "react";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import SettingsMenu from "@/components/SettingsMenu";
import { useGameSettings } from "@/lib/hooks/useGameSettings";
import { useSoundEffect } from "@/lib/hooks/useSoundEffect";
import { AUDIO_URLS, AUDIO_URLS_KEYS } from "@/lib/constants";

interface MultiplayerHeaderProps {
  onBack: () => void;
  title: string;
  icon?: React.ReactNode;
}

const MultiplayerHeader: React.FC<MultiplayerHeaderProps> = ({
  onBack,
  title,
  icon,
}) => {
  const { settings, updateSetting } = useGameSettings();
  const [settingsOpen, setSettingsOpen] = useState(false);

  const { playSound: playButtonClickSound } = useSoundEffect({
    audioUrl: AUDIO_URLS.BUTTON_CLICK,
    volume: 0.5,
    cacheKey: AUDIO_URLS_KEYS.BUTTON_CLICK,
  });

  const toggleDarkMode = () => {
    const nextDark = !settings.darkMode;
    updateSetting("darkMode", nextDark);
    document.cookie = `theme=${
      nextDark ? "dark" : "light"
    }; path=/; max-age=31536000`;
    playButtonClickSound();
  };

  const toggleSound = () => {
    const newValue = !settings.soundEffectsEnabled;
    updateSetting("soundEffectsEnabled", newValue);
    playButtonClickSound();
  };

  return (
    <div className="mb-8">
      <div className="flex items-center justify-center mb-3 sm:mb-6">
        <div className="bg-card rounded-2xl px-4 py-2 shadow border">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon-sm"
              onClick={onBack}
              className="text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="w-4 h-4" />
            </Button>
            
            <div className="w-px h-6 bg-border"></div>
            
            <div className="flex items-center gap-2">
              {icon}
              <span className="text-sm font-medium text-foreground">
                {title}
              </span>
            </div>

            <div className="w-px h-6 bg-border"></div>

            <SettingsMenu
              settingsOpen={settingsOpen}
              setSettingsOpen={setSettingsOpen}
              setShowDifficultyDialog={() => {}} // Not needed in multiplayer
              toggleSound={toggleSound}
              toggleDarkMode={toggleDarkMode}
              settings={settings}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default MultiplayerHeader;
